{"apiTests": {"health": {"status": "PASS", "responseTime": "N/A", "geminiAvailable": true, "data": {"success": true, "status": "healthy", "service": {"available": true, "cacheSize": 72, "rateLimitRequests": 9, "model": "gemini-1.5-flash-latest", "uptime": 2081.3705945, "timestamp": "2025-06-13T12:57:13.700Z"}}}, "zodiacSigns": {"status": "PASS", "signsCount": 12, "data": {"success": true, "signs": [{"key": "mesha", "name": "මේෂ", "symbol": "♈", "element": "ගින්න", "ruler": "අඟහරු", "dates": "මාර්තු 21 - අප්‍රේල් 19", "color": "#FF4444", "gemstone": "රතු කොරල්", "luckyNumbers": [1, 8, 17], "keywords": ["නායකත්වය", "ධෛර්යය", "ක්‍රියාශීලිත්වය", "නවෝත්පාදනය"]}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "name": "වෘෂභ", "symbol": "♉", "element": "පෘථිවි", "ruler": "සිකුරු", "dates": "අප්‍රේල් 20 - මැයි 20", "color": "#4CAF50", "gemstone": "මරකත", "luckyNumbers": [2, 6, 9], "keywords": ["ස්ථාවරත්වය", "විශ්වසනීයත්වය", "ඉවසීම", "ප්‍රායෝගිකත්වය"]}, {"key": "<PERSON><PERSON>a", "name": "මිථුන", "symbol": "♊", "element": "වායු", "ruler": "බුධ", "dates": "මැයි 21 - ජූනි 20", "color": "#FFD700", "gemstone": "පන්නා", "luckyNumbers": [5, 7, 14], "keywords": ["සන්නිවේදනය", "බුද්ධිමත්", "අනුවර්තනය", "කුතුහලය"]}, {"key": "karkata", "name": "කර්කට", "symbol": "♋", "element": "ජලය", "ruler": "චන්ද්‍රයා", "dates": "ජූනි 21 - ජූලි 22", "color": "#87CEEB", "gemstone": "මුතු", "luckyNumbers": [2, 7, 11], "keywords": ["සංවේදනය", "රැකවරණය", "අන්තර්ගත බුද්ධිය", "පවුල්කාමිත්වය"]}, {"key": "simha", "name": "සිංහ", "symbol": "♌", "element": "ගින්න", "ruler": "සූර්යයා", "dates": "ජූලි 23 - අගෝස්තු 22", "color": "#FF8C00", "gemstone": "රුබි", "luckyNumbers": [1, 3, 10], "keywords": ["නායකත්වය", "ආත්මවිශ්වාසය", "නිර්මාණශීලිත්වය", "ත්‍යාගශීලිත්වය"]}, {"key": "kanya", "name": "කන්‍යා", "symbol": "♍", "element": "පෘථිවි", "ruler": "බුධ", "dates": "අගෝස්තු 23 - සැප්තැම්බර් 22", "color": "#8FBC8F", "gemstone": "නීලමණි", "luckyNumbers": [6, 14, 18], "keywords": ["විශ්ලේෂණාත්මක", "පරිපූර්ණතාවාදී", "ප්‍රායෝගික", "සේවාශීලී"]}, {"key": "tula", "name": "තුලා", "symbol": "♎", "element": "වායු", "ruler": "සිකුරු", "dates": "සැප්තැම්බර් 23 - ඔක්තෝබර් 22", "color": "#FFB6C1", "gemstone": "ඔපල්", "luckyNumbers": [4, 6, 15], "keywords": ["සමතුලිතතාවය", "සාධාරණත්වය", "සමගිය", "රූපලාවණ්‍යය"]}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "name": "වෘශ්චික", "symbol": "♏", "element": "ජලය", "ruler": "අඟහරු", "dates": "ඔක්තෝබර් 23 - නොවැම්බර් 21", "color": "#8B0000", "gemstone": "ටොපාස්", "luckyNumbers": [8, 11, 18], "keywords": ["තීව්‍රතාවය", "පරිවර්තනය", "අභිරහස්", "දෘඪත්වය"]}, {"key": "dhanu", "name": "ධනු", "symbol": "♐", "element": "ගින්න", "ruler": "ගුරු", "dates": "නොවැම්බර් 22 - දෙසැම්බර් 21", "color": "#9370DB", "gemstone": "ටර්කොයිස්", "luckyNumbers": [3, 9, 22], "keywords": ["ගවේෂණය", "ප්‍රශස්තතාවය", "දර්ශනය", "ස්වාධීනත්වය"]}, {"key": "makara", "name": "මකර", "symbol": "♑", "element": "පෘථිවි", "ruler": "සෙනසුරු", "dates": "දෙසැම්බර් 22 - ජනවාරි 19", "color": "#2F4F4F", "gemstone": "ගාර්නට්", "luckyNumbers": [6, 9, 26], "keywords": ["අභිලාෂය", "විනය", "වගකීම", "ප්‍රායෝගිකත්වය"]}, {"key": "kumbha", "name": "කුම්භ", "symbol": "♒", "element": "වායු", "ruler": "සෙනසුරු", "dates": "ජනවාරි 20 - පෙබරවාරි 18", "color": "#00CED1", "gemstone": "ඇමතිස්ට්", "luckyNumbers": [4, 7, 11], "keywords": ["නවෝත්පාදනය", "මානවිකත්වය", "ස්වාධීනත්වය", "අනාගතවාදය"]}, {"key": "meena", "name": "මීන", "symbol": "♓", "element": "ජලය", "ruler": "ගුරු", "dates": "පෙබරවාරි 19 - මාර්තු 20", "color": "#20B2AA", "gemstone": "ඇක්වාමරීන්", "luckyNumbers": [3, 9, 12], "keywords": ["සංවේදනය", "අන්තර්ගත බුද්ධිය", "කරුණාව", "කලාත්මකත්වය"]}], "total": 12}}, "horoscope_mesha": {"status": "PASS", "responseTime": "2ms", "hasContent": true, "serviceAvailable": true, "cached": false}, "horoscope_vrishabha": {"status": "PASS", "responseTime": "2ms", "hasContent": true, "serviceAvailable": true, "cached": false}, "horoscope_mithuna": {"status": "PASS", "responseTime": "2ms", "hasContent": true, "serviceAvailable": true, "cached": false}, "horoscope_karkata": {"status": "PASS", "responseTime": "2ms", "hasContent": true, "serviceAvailable": true, "cached": false}, "horoscope_simha": {"status": "PASS", "responseTime": "3ms", "hasContent": true, "serviceAvailable": true, "cached": false}, "horoscope_kanya": {"status": "PASS", "responseTime": "2ms", "hasContent": true, "serviceAvailable": true, "cached": false}, "horoscope_tula": {"status": "PASS", "responseTime": "2ms", "hasContent": true, "serviceAvailable": true, "cached": false}, "horoscope_vrischika": {"status": "PASS", "responseTime": "2ms", "hasContent": true, "serviceAvailable": true, "cached": false}, "horoscope_dhanu": {"status": "PASS", "responseTime": "2ms", "hasContent": true, "serviceAvailable": true, "cached": false}, "horoscope_makara": {"status": "PASS", "responseTime": "1ms", "hasContent": true, "serviceAvailable": true, "cached": false}, "horoscope_kumbha": {"status": "PASS", "responseTime": "1ms", "hasContent": true, "serviceAvailable": true, "cached": false}, "horoscope_meena": {"status": "PASS", "responseTime": "1ms", "hasContent": true, "serviceAvailable": true, "cached": false}}, "pageTests": {"mesha": {"exists": true, "hasCorrectScripts": true, "hasInitialization": true, "hasCorrectTitle": true, "fileSize": "17KB"}, "vrishabha": {"exists": true, "hasCorrectScripts": true, "hasInitialization": true, "hasCorrectTitle": true, "fileSize": "18KB"}, "mithuna": {"exists": true, "hasCorrectScripts": true, "hasInitialization": true, "hasCorrectTitle": true, "fileSize": "18KB"}, "karkata": {"exists": true, "hasCorrectScripts": true, "hasInitialization": true, "hasCorrectTitle": true, "fileSize": "18KB"}, "simha": {"exists": true, "hasCorrectScripts": true, "hasInitialization": true, "hasCorrectTitle": true, "fileSize": "17KB"}, "kanya": {"exists": true, "hasCorrectScripts": true, "hasInitialization": true, "hasCorrectTitle": true, "fileSize": "18KB"}, "tula": {"exists": true, "hasCorrectScripts": true, "hasInitialization": true, "hasCorrectTitle": true, "fileSize": "17KB"}, "vrischika": {"exists": true, "hasCorrectScripts": true, "hasInitialization": true, "hasCorrectTitle": true, "fileSize": "18KB"}, "dhanu": {"exists": true, "hasCorrectScripts": true, "hasInitialization": true, "hasCorrectTitle": true, "fileSize": "18KB"}, "makara": {"exists": true, "hasCorrectScripts": true, "hasInitialization": true, "hasCorrectTitle": true, "fileSize": "18KB"}, "kumbha": {"exists": true, "hasCorrectScripts": true, "hasInitialization": true, "hasCorrectTitle": true, "fileSize": "18KB"}, "meena": {"exists": true, "hasCorrectScripts": true, "hasInitialization": true, "hasCorrectTitle": true, "fileSize": "18KB"}}, "scriptTests": {"scripts/env-config.js": {"exists": true, "fileSize": "5KB", "hasErrors": ["Potential undefined function calls"]}, "scripts/api-client.js": {"exists": true, "fileSize": "13KB", "hasErrors": ["Contains OpenAI references", "Potential undefined function calls"]}, "scripts/main.js": {"exists": true, "fileSize": "15KB", "hasErrors": []}, "scripts/zodiac.js": {"exists": true, "fileSize": "21KB", "hasErrors": []}, "scripts/config.js": {"exists": true, "fileSize": "1KB", "hasErrors": []}, "scripts/env-loader.js": {"exists": true, "fileSize": "1KB", "hasErrors": ["Potential undefined function calls"]}}, "errors": [], "summary": {}}