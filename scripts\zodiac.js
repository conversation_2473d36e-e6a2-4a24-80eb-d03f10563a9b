/**
 * Modern Zodiac Page JavaScript for Kubera Manthra
 * Uses new API client architecture with improved performance and error handling
 */

// Utility function to convert color codes to Sinhala color names
function getColorNameInSinhala(colorCode) {
    const colorMap = {
        '#FF4444': 'රතු', // Red (<PERSON><PERSON>)
        '#4CAF50': 'කොළ', // Green (Vrishabha)
        '#FFD700': 'රන්වන්', // Golden (Mithuna)
        '#87CEEB': 'අහස් නිල්', // Sky Blue (Karkata)
        '#FF8C00': 'තැඹිලි', // Orange (Simha)
        '#8FBC8F': 'ලා කොළ', // Light Green (Kanya)
        '#FFB6C1': 'රෝස', // Pink (Tula)
        '#8B0000': 'ගැඹුරු රතු', // Dark Red (Vrischika)
        '#9370DB': 'දම්', // Purple (Dhanu)
        '#2F4F4F': 'අඳුරු අළු', // Dark Slate Gray (Makara)
        '#00CED1': 'මැදගම් නිල්', // Medium Turquoise (Kumbha)
        '#20B2AA': 'මුහුදු කොළ' // Sea Green (Meena)
    };

    return colorMap[colorCode] || colorCode;
}

/**
 * Get current zodiac date range in Sinhala
 * @param {string} zodiacSign - Zodiac sign key
 * @returns {string} Formatted date range in Sinhala
 */
function getCurrentZodiacDateRange(zodiacSign) {
    const today = new Date();
    const currentYear = today.getFullYear();
    const currentMonth = today.getMonth() + 1;

    let year = currentYear;
    const isEarlyInYear = currentMonth <= 3;

    const zodiacRanges = {
        mesha: { start: `${year}-03-21`, end: `${year}-04-19` },
        vrishabha: { start: `${year}-04-20`, end: `${year}-05-20` },
        mithuna: { start: `${year}-05-21`, end: `${year}-06-20` },
        karkata: { start: `${year}-06-21`, end: `${year}-07-22` },
        simha: { start: `${year}-07-23`, end: `${year}-08-22` },
        kanya: { start: `${year}-08-23`, end: `${year}-09-22` },
        tula: { start: `${year}-09-23`, end: `${year}-10-22` },
        vrischika: { start: `${year}-10-23`, end: `${year}-11-21` },
        dhanu: { start: `${year}-11-22`, end: `${year}-12-21` },
        makara: { start: `${year}-12-22`, end: `${year+1}-01-19` },
        kumbha: {
            start: isEarlyInYear ? `${year-1}-01-20` : `${year}-01-20`,
            end: isEarlyInYear ? `${year-1}-02-18` : `${year}-02-18`
        },
        meena: {
            start: isEarlyInYear ? `${year-1}-02-19` : `${year}-02-19`,
            end: isEarlyInYear ? `${year}-03-20` : `${year+1}-03-20`
        }
    };

    const range = zodiacRanges[zodiacSign];
    if (!range) return '';

    const startDate = new Date(range.start);
    const endDate = new Date(range.end);

    const months = {
        1: 'ජනවාරි', 2: 'පෙබරවාරි', 3: 'මාර්තු', 4: 'අප්‍රේල්',
        5: 'මැයි', 6: 'ජූනි', 7: 'ජූලි', 8: 'අගෝස්තු',
        9: 'සැප්තැම්බර්', 10: 'ඔක්තෝබර්', 11: 'නොවැම්බර්', 12: 'දෙසැම්බර්'
    };

    return `${months[startDate.getMonth() + 1]} ${startDate.getDate()} - ${months[endDate.getMonth() + 1]} ${endDate.getDate()}`;
}

/**
 * Update zodiac date ranges in UI elements
 */
function updateZodiacDateRanges() {
    const zodiacDates = document.querySelectorAll('.zodiac-dates');
    const currentZodiacSign = window.location.pathname.match(/\/pages\/([^\/]+)\.html$/);

    if (currentZodiacSign) {
        const sign = currentZodiacSign[1];
        zodiacDates.forEach(element => {
            element.textContent = getCurrentZodiacDateRange(sign);
        });
    }
}

/**
 * Modern Zodiac Page Manager
 * Handles zodiac page initialization and content management
 */
class ZodiacPageManager {
    constructor() {
        this.currentZodiac = '';
        this.zodiacConfig = null;
        this.isInitialized = false;
    }

    /**
     * Initialize zodiac page
     * @param {string} zodiacSign - Zodiac sign key
     */
    async initialize(zodiacSign) {
        if (this.isInitialized && this.currentZodiac === zodiacSign) {
            return;
        }

        this.currentZodiac = zodiacSign;

        try {
            console.log(`Initializing zodiac page for ${zodiacSign}`);

            // Load zodiac configuration
            await this.loadZodiacConfig();

            // Load cached content first for immediate display
            this.loadCachedContent();

            // Check if content needs refresh
            if (this.shouldRefreshContent()) {
                await this.refreshDailyContent();
            }

            // Update UI elements
            this.updateLastRefreshTime();
            updateZodiacDateRanges();
            this.setupEventListeners();

            this.isInitialized = true;
            console.log(`Zodiac page initialized successfully for ${zodiacSign}`);

        } catch (error) {
            console.error(`Failed to initialize zodiac page for ${zodiacSign}:`, error.message);
            this.showFallbackContent();
        }
    }

    /**
     * Load zodiac configuration from API
     */
    async loadZodiacConfig() {
        try {
            const response = await apiClient.getZodiacSigns();
            if (response.success && response.signs) {
                const sign = response.signs.find(s => s.key === this.currentZodiac);
                if (sign) {
                    this.zodiacConfig = sign;
                } else {
                    throw new Error(`Zodiac sign ${this.currentZodiac} not found`);
                }
            } else {
                throw new Error('Failed to load zodiac configuration');
            }
        } catch (error) {
            console.warn('Using fallback zodiac configuration:', error.message);
            this.zodiacConfig = this.getFallbackZodiacConfig();
        }
    }

    /**
     * Get fallback zodiac configuration
     * @returns {Object}
     */
    getFallbackZodiacConfig() {
        const fallbackConfigs = {
            mesha: { name: 'මේෂ', symbol: '♈', color: '#FF4444' },
            vrishabha: { name: 'වෘෂභ', symbol: '♉', color: '#4CAF50' },
            mithuna: { name: 'මිථුන', symbol: '♊', color: '#FFD700' },
            karkata: { name: 'කර්කට', symbol: '♋', color: '#87CEEB' },
            simha: { name: 'සිංහ', symbol: '♌', color: '#FF8C00' },
            kanya: { name: 'කන්‍යා', symbol: '♍', color: '#8FBC8F' },
            tula: { name: 'තුලා', symbol: '♎', color: '#FFB6C1' },
            vrischika: { name: 'වෘශ්චික', symbol: '♏', color: '#8B0000' },
            dhanu: { name: 'ධනු', symbol: '♐', color: '#9370DB' },
            makara: { name: 'මකර', symbol: '♑', color: '#2F4F4F' },
            kumbha: { name: 'කුම්භ', symbol: '♒', color: '#00CED1' },
            meena: { name: 'මීන', symbol: '♓', color: '#20B2AA' }
        };

        return fallbackConfigs[this.currentZodiac] || {
            name: this.currentZodiac,
            symbol: '⭐',
            color: '#666666'
        };
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Refresh button
        const refreshBtn = document.getElementById('refreshBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.refreshDailyContent());
        }

        // Auto-refresh every hour
        setInterval(() => {
            if (this.shouldRefreshContent()) {
                this.refreshDailyContent();
            }
        }, 3600000); // 1 hour
    }

    /**
     * Check if content should be refreshed
     * @returns {boolean}
     */
    shouldRefreshContent() {
        const lastRefresh = localStorage.getItem(`lastRefresh_${this.currentZodiac}`);
        if (!lastRefresh) return true;

        const lastRefreshDate = new Date(lastRefresh);
        const now = new Date();

        // Refresh if it's a new day
        return lastRefreshDate.toDateString() !== now.toDateString();
    }

    /**
     * Load cached content from localStorage
     */
    loadCachedContent() {
        const cachedData = localStorage.getItem(`dailyContent_${this.currentZodiac}`);
        if (cachedData) {
            try {
                const parsedData = JSON.parse(cachedData);
                this.displayDailyContent(parsedData);
                console.log(`Loaded cached content for ${this.currentZodiac}`);
            } catch (error) {
                console.error('Error loading cached content:', error);
                this.showFallbackContent();
            }
        } else {
            this.showFallbackContent();
        }
    }

    /**
     * Refresh daily content using new API client
     */
    async refreshDailyContent() {
        const refreshBtn = document.getElementById('refreshBtn');

        if (refreshBtn) {
            refreshBtn.disabled = true;
            refreshBtn.innerHTML = '<span class="loading"></span> යාවත්කාලීන වෙමින්...';
        }

        // Clear any existing loading timeout
        this.clearLoadingTimeout();

        try {
            // Show loading state
            this.showLoadingState();

            // Check for network connectivity
            if (!navigator.onLine) {
                throw new Error('No internet connection');
            }

            console.log(`Refreshing content for ${this.currentZodiac}`);

            // Get fresh content from API with timeout
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('Operation timeout')), 120000); // 2 minutes
            });

            const response = await Promise.race([
                apiClient.getDailyHoroscope(this.currentZodiac),
                timeoutPromise
            ]);

            if (response.success) {
                // Cache the content
                try {
                    localStorage.setItem(`dailyContent_${this.currentZodiac}`, JSON.stringify(response));
                    localStorage.setItem(`lastRefresh_${this.currentZodiac}`, new Date().toISOString());
                } catch (storageError) {
                    console.warn('Failed to cache content in localStorage:', storageError);
                }

                // Display the content
                this.displayDailyContent(response);

                // Update last refresh time
                this.updateLastRefreshTime();

                // Show success message
                const message = response.metadata?.serviceAvailable ?
                    'දෛනික අන්තර්ගතය සාර්ථකව යාවත්කාලීන කරන ලදී!' :
                    'ස්ථිතික අන්තර්ගතය පෙන්වනු ලැබේ.';

                this.showNotification(message, 'success');

            } else {
                throw new Error(response.error || 'Failed to fetch horoscope data');
            }

        } catch (error) {
            console.error('Error refreshing content:', error);

            // Show appropriate error message and fallback
            if (error.message === 'No internet connection') {
                this.showNotification('අන්තර්ජාල සම්බන්ධතාවය නැත. කරුණාකර ඔබේ සම්බන්ධතාවය පරීක්ෂා කර නැවත උත්සාහ කරන්න.', 'error');
                this.showFallbackContent();
            } else if (error.message.includes('timeout') || error.message === 'Operation timeout') {
                this.showNotification('සේවාව මන්දගාමී වී ඇත. ස්ථිතික අන්තර්ගතය පෙන්වනු ලැබේ.', 'warning');
                this.showFallbackContent();
            } else {
                this.showNotification('අන්තර්ගතය යාවත්කාලීන කිරීමේදී දෝෂයක් ඇතිවිය. ස්ථිතික අන්තර්ගතය පෙන්වනු ලැබේ.', 'warning');
                this.showFallbackContent();
            }

        } finally {
            // Clear loading timeout and reset refresh button
            this.clearLoadingTimeout();

            if (refreshBtn) {
                refreshBtn.disabled = false;
                refreshBtn.innerHTML = '<span class="refresh-icon">🔄</span> යාවත්කාලීන කරන්න';
            }
        }
    }

    /**
     * Display daily content in UI elements
     * @param {Object} response - API response with content
     */
    displayDailyContent(response) {
        const content = response.content || response;
        const contentElements = {
            dailyPrediction: document.getElementById('dailyPrediction'),
            wealthForecast: document.getElementById('wealthForecast'),
            loveForecast: document.getElementById('loveForecast'),
            healthForecast: document.getElementById('healthForecast'),
            auspiciousTime: document.getElementById('auspiciousTime'),
            luckyColor: document.getElementById('luckyColor')
        };

        Object.entries(contentElements).forEach(([key, element]) => {
            if (element && content[key]) {
                element.innerHTML = `<p>${content[key]}</p>`;
                element.classList.remove('fade-in');
                element.style.opacity = '1';
                element.style.transform = 'translateY(0)';

                // Force reflow and add animation
                void element.offsetWidth;
                element.classList.add('fade-in');
            }
        });

        // Update zodiac info if available
        if (response.zodiacConfig) {
            this.updateZodiacInfo(response.zodiacConfig);
        }
    }

    /**
     * Update zodiac information in UI
     * @param {Object} zodiacConfig - Zodiac configuration
     */
    updateZodiacInfo(zodiacConfig) {
        const elements = {
            zodiacName: document.querySelector('.zodiac-name'),
            zodiacSymbol: document.querySelector('.zodiac-symbol'),
            zodiacElement: document.querySelector('.zodiac-element'),
            zodiacRuler: document.querySelector('.zodiac-ruler'),
            zodiacGemstone: document.querySelector('.zodiac-gemstone')
        };

        if (elements.zodiacName) elements.zodiacName.textContent = zodiacConfig.name;
        if (elements.zodiacSymbol) elements.zodiacSymbol.textContent = zodiacConfig.symbol;
        if (elements.zodiacElement) elements.zodiacElement.textContent = zodiacConfig.element;
        if (elements.zodiacRuler) elements.zodiacRuler.textContent = zodiacConfig.ruler;
        if (elements.zodiacGemstone) elements.zodiacGemstone.textContent = zodiacConfig.gemstone;
    }

    /**
     * Show loading state in content elements with progress indication
     */
    showLoadingState() {
        const loadingElements = [
            'dailyPrediction', 'wealthForecast', 'loveForecast',
            'healthForecast', 'auspiciousTime', 'luckyColor'
        ];

        loadingElements.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.innerHTML = `
                    <div class="loading-content">
                        <div class="loading-spinner"></div>
                        <div class="loading-text">
                            <div class="loading-message">අන්තර්ගතය ජනනය වෙමින්...</div>
                            <div class="loading-submessage">කරුණාකර ඉවසන්න, මෙය මිනිත්තු 1-2ක් ගත විය හැකිය</div>
                        </div>
                    </div>
                `;
            }
        });

        // Add timeout warning after 30 seconds
        this.loadingTimeout = setTimeout(() => {
            this.showLoadingWarning();
        }, 30000);
    }

    /**
     * Show warning when loading takes too long
     */
    showLoadingWarning() {
        const loadingElements = [
            'dailyPrediction', 'wealthForecast', 'loveForecast',
            'healthForecast', 'auspiciousTime', 'luckyColor'
        ];

        loadingElements.forEach(id => {
            const element = document.getElementById(id);
            if (element && element.innerHTML.includes('loading-content')) {
                element.innerHTML = `
                    <div class="loading-content warning">
                        <div class="loading-spinner slow"></div>
                        <div class="loading-text">
                            <div class="loading-message">අන්තර්ගතය ජනනය වෙමින්...</div>
                            <div class="loading-submessage">සේවාව මන්දගාමී වී ඇත. කරුණාකර ඉවසන්න.</div>
                            <div class="loading-tip">ඔබට පිටුව නැවුම් කිරීමට හෝ පසුව නැවත උත්සාහ කිරීමට හැකිය.</div>
                        </div>
                    </div>
                `;
            }
        });
    }

    /**
     * Clear loading timeout
     */
    clearLoadingTimeout() {
        if (this.loadingTimeout) {
            clearTimeout(this.loadingTimeout);
            this.loadingTimeout = null;
        }
    }

    /**
     * Show fallback content when API is not available
     */
    showFallbackContent() {
        const fallbackContent = {
            dailyPrediction: `${this.zodiacConfig?.name || this.currentZodiac} ලග්නයේ පුද්ගලයන්ට අද දිනය ධනාත්මක අත්දැකීම් ගෙන එනු ඇත.`,
            wealthForecast: `ධන කටයුතුවල ප්‍රවේශම්කාරී තීරණ ගැනීමෙන් වළකින්න. පැරණි ආයෝජන සමාලෝචනය කර නව අවස්ථා සඳහා සැලසුම් කරන්න.`,
            loveForecast: `ප්‍රේම සම්බන්ධතාවල අවබෝධය සහ ඉවසීම වැදගත් වේ. පවුල් සාමාජිකයන් සමඟ ගුණාත්මක කාලය ගත කරන්න.`,
            healthForecast: `ශාරීරික සහ මානසික සෞඛ්‍යය සඳහා සමතුලිත ජීවන රටාවක් පවත්වන්න. ප්‍රමාණවත් විවේකය ගන්න සහ සෞඛ්‍ය සම්පන්න ආහාර ගන්න.`,
            auspiciousTime: `උදෑසන 6:00 - 8:00 සහ සවස 6:00 - 8:00 අතර කාල වේලාවන් ඔබට ශුභ වේ. වැදගත් කටයුතු මෙම කාල වේලාවන්හි ආරම්භ කරන්න.`,
            luckyColor: `අද දිනය සඳහා නිල් සහ සුදු වර්ණ ශුභ වේ. මෙම වර්ණයේ ඇඳුම් හෝ උපාංග භාවිතා කිරීමෙන් ධනාත්මක ශක්තිය ලබා ගත හැකිය.`
        };

        this.displayDailyContent({ content: fallbackContent });
    }

    /**
     * Update last refresh time display
     */
    updateLastRefreshTime() {
        const updateTimeElement = document.getElementById('updateTime');
        if (updateTimeElement) {
            const lastRefresh = localStorage.getItem(`lastRefresh_${this.currentZodiac}`);
            if (lastRefresh) {
                const date = new Date(lastRefresh);
                updateTimeElement.textContent = date.toLocaleString('si-LK');
            } else {
                updateTimeElement.textContent = 'කිසි විටෙකත් නැත';
            }
        }
    }

    /**
     * Show notification to user
     * @param {string} message - Notification message
     * @param {string} type - Notification type ('success', 'error', 'info', 'warning')
     */
    showNotification(message, type = 'info') {
        // Remove existing notifications
        const existingNotifications = document.querySelectorAll('.notification');
        existingNotifications.forEach(notification => notification.remove());

        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <span class="notification-message">${message}</span>
                <button class="notification-close" onclick="this.parentElement.parentElement.remove()">&times;</button>
            </div>
        `;

        // Add styles
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            max-width: 400px;
            animation: slideIn 0.3s ease;
        `;

        // Set background color based on type
        const colors = {
            success: '#d4edda',
            error: '#f8d7da',
            info: '#d1ecf1',
            warning: '#fff3cd'
        };

        notification.style.backgroundColor = colors[type] || colors.info;

        // Add to page
        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }
}

// Create global instance of zodiac page manager
const zodiacPageManager = new ZodiacPageManager();

/**
 * Initialize zodiac page (legacy function for backward compatibility)
 * @param {string} zodiacSign - Zodiac sign key
 */
async function initializeZodiacPage(zodiacSign) {
    await zodiacPageManager.initialize(zodiacSign);
}

/**
 * Refresh daily content (legacy function for backward compatibility)
 */
async function refreshDailyContent() {
    await zodiacPageManager.refreshDailyContent();
}

/**
 * Update all zodiac cards on index page
 */
function updateAllZodiacCards() {
    const zodiacCards = document.querySelectorAll('.zodiac-card');

    zodiacCards.forEach(card => {
        const sign = card.getAttribute('data-sign');
        if (sign) {
            const dateElement = card.querySelector('.card-front p');
            if (dateElement) {
                dateElement.textContent = getCurrentZodiacDateRange(sign);
            }
        }
    });
}

// Add CSS for animations and notifications
const zodiacStyles = document.createElement('style');
zodiacStyles.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .notification-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 1rem;
    }

    .notification-close {
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        opacity: 0.7;
        transition: opacity 0.2s;
    }

    .notification-close:hover {
        opacity: 1;
    }

    .fade-in {
        animation: fadeIn 0.5s ease;
    }

    .loading-content {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        opacity: 0.7;
    }

    .loading {
        width: 16px;
        height: 16px;
        border: 2px solid #f3f3f3;
        border-top: 2px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
`;
document.head.appendChild(zodiacStyles);

// Export functions for global use
window.initializeZodiacPage = initializeZodiacPage;
window.refreshDailyContent = refreshDailyContent;
window.updateAllZodiacCards = updateAllZodiacCards;
window.getCurrentZodiacDateRange = getCurrentZodiacDateRange;
window.ZodiacPageManager = ZodiacPageManager;

// Initialize on page load
document.addEventListener('DOMContentLoaded', async function() {
    console.log('Zodiac page script loaded');

    // Auto-detect zodiac sign from URL
    const path = window.location.pathname;
    const zodiacMatch = path.match(/\/pages\/(\w+)\.html$/);

    if (zodiacMatch) {
        // This is a zodiac detail page
        const zodiacSign = zodiacMatch[1];
        console.log(`Detected zodiac page: ${zodiacSign}`);
        await initializeZodiacPage(zodiacSign);
    } else if (path === '/' || path.endsWith('index.html')) {
        // This is the index page with all zodiac cards
        console.log('Detected index page, updating zodiac cards');
        updateAllZodiacCards();
    }
});

// Service Worker registration for offline functionality
if ('serviceWorker' in navigator) {
    navigator.serviceWorker.register('/sw.js')
        .then(registration => {
            console.log('Service Worker registered successfully');
        })
        .catch(error => {
            console.warn('Service Worker registration failed:', error);
        });
}