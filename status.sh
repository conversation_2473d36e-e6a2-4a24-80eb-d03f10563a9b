#!/bin/bash

# Kubera Manthra Status Check Script
# Quick status check for production deployment

echo "📊 Kubera Manthra Status Check"
echo "=============================="
echo "Domain: thepostcloud.info"
echo "Server: *************"
echo "=============================="

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

check_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

# Check PM2 status
echo "🔍 PM2 Application Status:"
pm2 status 2>/dev/null
echo ""

# Check if application is running
pm2 list | grep -q kubera-manthra
check_status $? "PM2 process running"

# Check if port 3001 is listening
netstat -tlnp | grep -q ":3001"
check_status $? "Port 3001 listening"

# Check Nginx status
systemctl is-active --quiet nginx
check_status $? "Nginx service running"

# Test API endpoints
echo ""
echo "🧪 API Endpoint Tests:"
echo "====================="

# Test health endpoint
curl -s -f http://localhost:3001/api/health > /dev/null
check_status $? "Health endpoint (direct)"

curl -s -f http://localhost/api/health > /dev/null
check_status $? "Health endpoint (nginx)"

# Test zodiac signs endpoint
curl -s -f http://localhost:3001/api/zodiac-signs > /dev/null
check_status $? "Zodiac signs endpoint"

echo ""
echo "💾 System Resources:"
echo "===================="
echo "Memory: $(free -h | grep Mem | awk '{print $3"/"$2}')"
echo "Disk: $(df -h / | awk 'NR==2{print $3"/"$2" ("$5" used)"}')"
echo "Load: $(uptime | awk -F'load average:' '{print $2}')"

echo ""
echo "📝 Recent Logs (last 5 lines):"
echo "==============================="
pm2 logs kubera-manthra --lines 5 --nostream 2>/dev/null || echo "No logs available"

echo ""
echo "🔗 Quick Test URLs:"
echo "=================="
echo "curl http://localhost:3001/api/health"
echo "curl http://thepostcloud.info/api/health"
echo "curl http://*************/api/health"
