<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kubera Manthra - Browser Test Suite</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 3px;
        }
        .pass { background: #d4edda; color: #155724; }
        .fail { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .zodiac-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        .zodiac-test {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            text-align: center;
        }
        .error-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Kubera Manthra Browser Test Suite</h1>
        <p>Comprehensive browser-side testing for all application components</p>

        <div class="test-section">
            <h2>🔧 Environment Tests</h2>
            <button onclick="testEnvironment()">Test Environment Setup</button>
            <div id="envResults"></div>
        </div>

        <div class="test-section">
            <h2>🌐 API Connection Tests</h2>
            <button onclick="testApiConnections()">Test All API Endpoints</button>
            <div id="apiResults"></div>
        </div>

        <div class="test-section">
            <h2>🔮 Zodiac Page Tests</h2>
            <button onclick="testZodiacPages()">Test All Zodiac Pages</button>
            <div id="zodiacResults"></div>
        </div>

        <div class="test-section">
            <h2>💾 Cache Management Tests</h2>
            <button onclick="testCacheManagement()">Test Cache Functions</button>
            <div id="cacheResults"></div>
        </div>

        <div class="test-section">
            <h2>🐛 Error Detection</h2>
            <button onclick="captureErrors()">Capture Console Errors</button>
            <div id="errorResults"></div>
            <div id="errorLog" class="error-log" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>📊 Performance Tests</h2>
            <button onclick="testPerformance()">Test Load Performance</button>
            <div id="performanceResults"></div>
        </div>
    </div>

    <script>
        // Global error capture
        const capturedErrors = [];
        const originalConsoleError = console.error;
        console.error = function(...args) {
            capturedErrors.push({
                timestamp: new Date().toISOString(),
                message: args.join(' '),
                stack: new Error().stack
            });
            originalConsoleError.apply(console, args);
        };

        window.addEventListener('error', (e) => {
            capturedErrors.push({
                timestamp: new Date().toISOString(),
                message: e.message,
                filename: e.filename,
                lineno: e.lineno,
                colno: e.colno,
                stack: e.error?.stack
            });
        });

        const zodiacSigns = [
            'mesha', 'vrishabha', 'mithuna', 'karkata',
            'simha', 'kanya', 'tula', 'vrischika',
            'dhanu', 'makara', 'kumbha', 'meena'
        ];

        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            container.appendChild(div);
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        async function testEnvironment() {
            clearResults('envResults');
            addResult('envResults', '🔍 Testing environment setup...', 'info');

            // Test if required scripts are loaded
            const requiredGlobals = ['apiClient', 'ClientEnvironment', 'showServiceStatus'];
            requiredGlobals.forEach(global => {
                if (window[global]) {
                    addResult('envResults', `✅ ${global} is available`, 'pass');
                } else {
                    addResult('envResults', `❌ ${global} is missing`, 'fail');
                }
            });

            // Test API client initialization
            if (window.apiClient) {
                try {
                    const health = await window.apiClient.checkServiceHealth();
                    addResult('envResults', `✅ API client health check: ${health ? 'PASS' : 'FAIL'}`, health ? 'pass' : 'warning');
                } catch (error) {
                    addResult('envResults', `❌ API client health check failed: ${error.message}`, 'fail');
                }
            }
        }

        async function testApiConnections() {
            clearResults('apiResults');
            addResult('apiResults', '🌐 Testing API connections...', 'info');

            const endpoints = [
                '/api/health',
                '/api/zodiac-signs',
                '/api/daily-horoscope/mesha'
            ];

            for (const endpoint of endpoints) {
                try {
                    const startTime = Date.now();
                    const response = await fetch(endpoint);
                    const responseTime = Date.now() - startTime;
                    const data = await response.json();

                    if (response.ok) {
                        addResult('apiResults', `✅ ${endpoint}: PASS (${responseTime}ms)`, 'pass');
                    } else {
                        addResult('apiResults', `❌ ${endpoint}: FAIL - ${data.error || 'Unknown error'}`, 'fail');
                    }
                } catch (error) {
                    addResult('apiResults', `❌ ${endpoint}: ERROR - ${error.message}`, 'fail');
                }
            }
        }

        async function testZodiacPages() {
            clearResults('zodiacResults');
            addResult('zodiacResults', '🔮 Testing zodiac page functionality...', 'info');

            const zodiacGrid = document.createElement('div');
            zodiacGrid.className = 'zodiac-grid';
            document.getElementById('zodiacResults').appendChild(zodiacGrid);

            for (const sign of zodiacSigns) {
                const testDiv = document.createElement('div');
                testDiv.className = 'zodiac-test';
                testDiv.innerHTML = `<strong>${sign}</strong><br><span id="test-${sign}">Testing...</span>`;
                zodiacGrid.appendChild(testDiv);

                try {
                    // Test API endpoint for this sign
                    const response = await fetch(`/api/daily-horoscope/${sign}`);
                    const data = await response.json();

                    if (response.ok && data.success) {
                        document.getElementById(`test-${sign}`).innerHTML = '✅ PASS';
                        testDiv.classList.add('pass');
                    } else {
                        document.getElementById(`test-${sign}`).innerHTML = '❌ FAIL';
                        testDiv.classList.add('fail');
                    }
                } catch (error) {
                    document.getElementById(`test-${sign}`).innerHTML = '❌ ERROR';
                    testDiv.classList.add('fail');
                }
            }
        }

        async function testCacheManagement() {
            clearResults('cacheResults');
            addResult('cacheResults', '💾 Testing cache management...', 'info');

            if (window.apiClient) {
                try {
                    // Test cache stats
                    const stats = window.apiClient.getCacheStats();
                    addResult('cacheResults', `✅ Cache stats retrieved: ${JSON.stringify(stats)}`, 'pass');

                    // Test cache clearing
                    if (window.clearAllCaches) {
                        addResult('cacheResults', '✅ Cache clearing function available', 'pass');
                    } else {
                        addResult('cacheResults', '❌ Cache clearing function missing', 'fail');
                    }

                    // Test localStorage access
                    try {
                        localStorage.setItem('test-key', 'test-value');
                        const value = localStorage.getItem('test-key');
                        localStorage.removeItem('test-key');
                        addResult('cacheResults', '✅ localStorage access working', 'pass');
                    } catch (error) {
                        addResult('cacheResults', `❌ localStorage access failed: ${error.message}`, 'fail');
                    }

                } catch (error) {
                    addResult('cacheResults', `❌ Cache management test failed: ${error.message}`, 'fail');
                }
            } else {
                addResult('cacheResults', '❌ API client not available for cache testing', 'fail');
            }
        }

        function captureErrors() {
            clearResults('errorResults');
            const errorLog = document.getElementById('errorLog');

            if (capturedErrors.length === 0) {
                addResult('errorResults', '✅ No JavaScript errors detected', 'pass');
                errorLog.style.display = 'none';
            } else {
                addResult('errorResults', `❌ ${capturedErrors.length} JavaScript errors detected`, 'fail');
                errorLog.style.display = 'block';
                errorLog.innerHTML = capturedErrors.map(error => 
                    `<div><strong>${error.timestamp}</strong><br>${error.message}<br><small>${error.stack || error.filename}:${error.lineno}</small></div><hr>`
                ).join('');
            }

            // Check for common issues
            if (typeof apiClient === 'undefined') {
                addResult('errorResults', '❌ apiClient is not defined', 'fail');
            }
            if (typeof initializeZodiacPage === 'undefined') {
                addResult('errorResults', '❌ initializeZodiacPage function is not defined', 'fail');
            }
        }

        async function testPerformance() {
            clearResults('performanceResults');
            addResult('performanceResults', '📊 Testing performance...', 'info');

            // Test script loading times
            const scripts = document.querySelectorAll('script[src]');
            addResult('performanceResults', `✅ ${scripts.length} scripts loaded`, 'pass');

            // Test API response times
            try {
                const startTime = performance.now();
                const response = await fetch('/api/health');
                const endTime = performance.now();
                const responseTime = Math.round(endTime - startTime);

                if (responseTime < 1000) {
                    addResult('performanceResults', `✅ API response time: ${responseTime}ms (Good)`, 'pass');
                } else if (responseTime < 3000) {
                    addResult('performanceResults', `⚠️ API response time: ${responseTime}ms (Acceptable)`, 'warning');
                } else {
                    addResult('performanceResults', `❌ API response time: ${responseTime}ms (Slow)`, 'fail');
                }
            } catch (error) {
                addResult('performanceResults', `❌ Performance test failed: ${error.message}`, 'fail');
            }

            // Test memory usage (if available)
            if (performance.memory) {
                const memory = performance.memory;
                const usedMB = Math.round(memory.usedJSHeapSize / 1024 / 1024);
                addResult('performanceResults', `📊 Memory usage: ${usedMB}MB`, 'info');
            }
        }

        // Auto-run basic tests on page load
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                testEnvironment();
                captureErrors();
            }, 1000);
        });
    </script>
</body>
</html>
