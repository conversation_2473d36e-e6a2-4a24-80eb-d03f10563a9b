# Kubera Manthra - Ubuntu EC2 Quick Start

## 🚀 Quick Deployment on Ubuntu EC2

**Server**: *************  
**Domain**: thepostcloud.info  
**OS**: Ubuntu EC2

## Step-by-Step Deployment

### 1. Connect to Your Ubuntu EC2 Instance
```bash
ssh -i your-key.pem ubuntu@*************
```

### 2. Upload Your Application Files
```bash
# Option 1: Using SCP from your local machine
scp -i your-key.pem -r . ubuntu@*************:~/kubera-manthra/

# Option 2: Using Git (if your code is in a repository)
git clone your-repository-url kubera-manthra
cd kubera-manthra
```

### 3. Run the Deployment Script
```bash
# Make the script executable
chmod +x deploy.sh

# Run the deployment
./deploy.sh
```

The deployment script will automatically:
- ✅ Update Ubuntu packages
- ✅ Install Node.js 18.x
- ✅ Install Nginx
- ✅ Install PM2
- ✅ Configure the application
- ✅ Set up reverse proxy
- ✅ Start all services

### 4. Verify Deployment
```bash
# Check application status
./status.sh

# Test API endpoints
curl http://localhost:3001/api/health
curl http://thepostcloud.info/api/health
```

## 🔧 Manual Installation (if needed)

### Install Node.js
```bash
# Update packages
sudo apt update && sudo apt upgrade -y

# Install Node.js 18.x
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs

# Verify installation
node --version
npm --version
```

### Install Nginx
```bash
# Install Nginx
sudo apt install -y nginx

# Start and enable Nginx
sudo systemctl start nginx
sudo systemctl enable nginx

# Check status
sudo systemctl status nginx
```

### Install PM2
```bash
# Install PM2 globally
sudo npm install -g pm2

# Verify installation
pm2 --version
```

### Start Your Application
```bash
# Install dependencies
npm install --production

# Start with PM2
pm2 start ecosystem.config.js --env production

# Save PM2 configuration
pm2 save

# Setup PM2 startup
pm2 startup
```

## 🔒 Security Configuration

### Configure UFW Firewall
```bash
# Enable UFW
sudo ufw enable

# Allow SSH
sudo ufw allow ssh

# Allow HTTP and HTTPS
sudo ufw allow 80
sudo ufw allow 443

# Allow application port (optional)
sudo ufw allow 3001

# Check status
sudo ufw status
```

### SSL Certificate Setup
```bash
# Install Certbot
sudo apt install -y certbot python3-certbot-nginx

# Get SSL certificate
sudo certbot --nginx -d thepostcloud.info -d www.thepostcloud.info

# Test auto-renewal
sudo certbot renew --dry-run
```

## 📊 Monitoring Commands

### Application Monitoring
```bash
# Check PM2 status
pm2 status

# View logs
pm2 logs kubera-manthra

# Monitor resources
pm2 monit

# Restart application
pm2 restart kubera-manthra
```

### System Monitoring
```bash
# Check system resources
htop

# Check disk usage
df -h

# Check memory usage
free -h

# Check running processes
ps aux | grep node
```

### Nginx Monitoring
```bash
# Check Nginx status
sudo systemctl status nginx

# View Nginx logs
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# Test Nginx configuration
sudo nginx -t

# Restart Nginx
sudo systemctl restart nginx
```

## 🛠️ Troubleshooting

### Common Issues

1. **Permission Denied**
   ```bash
   # Fix file permissions
   chmod +x deploy.sh status.sh
   
   # Fix directory permissions
   sudo chown -R ubuntu:ubuntu ~/kubera-manthra
   ```

2. **Port Already in Use**
   ```bash
   # Check what's using port 3001
   sudo netstat -tlnp | grep 3001
   
   # Kill process if needed
   sudo kill -9 PID_NUMBER
   ```

3. **Nginx Configuration Error**
   ```bash
   # Test configuration
   sudo nginx -t
   
   # View error logs
   sudo tail -20 /var/log/nginx/error.log
   ```

4. **PM2 Process Not Starting**
   ```bash
   # Check PM2 logs
   pm2 logs kubera-manthra
   
   # Restart PM2
   pm2 restart kubera-manthra
   
   # Delete and recreate
   pm2 delete kubera-manthra
   pm2 start ecosystem.config.js --env production
   ```

## 🔄 Updates and Maintenance

### Update Application
```bash
# Pull latest changes (if using Git)
git pull origin main

# Install new dependencies
npm install --production

# Restart application
pm2 restart kubera-manthra
```

### Update System
```bash
# Update Ubuntu packages
sudo apt update && sudo apt upgrade -y

# Update Node.js packages
npm update

# Update PM2
sudo npm update -g pm2
```

## 📋 Quick Reference

### Essential Commands
```bash
# Application status
./status.sh

# Start application
pm2 start ecosystem.config.js --env production

# Stop application
pm2 stop kubera-manthra

# Restart application
pm2 restart kubera-manthra

# View logs
pm2 logs kubera-manthra

# Test API
curl http://thepostcloud.info/api/health
```

### File Locations
- **Application**: `~/kubera-manthra/`
- **Logs**: `~/kubera-manthra/logs/`
- **Nginx Config**: `/etc/nginx/conf.d/kubera-manthra.conf`
- **PM2 Logs**: `~/.pm2/logs/`

## 🎉 Success!

After following these steps, your Kubera Manthra application should be running at:
- **Main Site**: http://thepostcloud.info
- **API Health**: http://thepostcloud.info/api/health
- **Direct IP**: http://*************

Your application is now production-ready on Ubuntu EC2!
