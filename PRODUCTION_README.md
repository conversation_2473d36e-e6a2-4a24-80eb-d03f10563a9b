# Kubera Manthra - Production Deployment

## 🌐 Live Application
- **Domain**: [thepostcloud.info](http://thepostcloud.info)
- **Server**: AWS EC2 (*************)
- **Status**: Production Ready
- **API Health**: [thepostcloud.info/api/health](http://thepostcloud.info/api/health)

## 🚀 Quick Deployment

### Prerequisites
- AWS EC2 instance running Amazon Linux 2
- Domain DNS pointing to your server IP
- SSH access to your server

### One-Command Deployment
```bash
# On your EC2 server
chmod +x deploy.sh && ./deploy.sh
```

This will automatically:
- ✅ Install Node.js, Nginx, and PM2
- ✅ Configure the application
- ✅ Set up reverse proxy
- ✅ Start all services
- ✅ Test the deployment

## 📊 Management Commands

### Application Management
```bash
# Check status
./status.sh

# View application status
pm2 status

# View logs
pm2 logs kubera-manthra

# Restart application
pm2 restart kubera-manthra

# Stop application
pm2 stop kubera-manthra
```

### System Management
```bash
# Check Nginx status
sudo systemctl status nginx

# Restart Nginx
sudo systemctl restart nginx

# View Nginx logs
sudo tail -f /var/log/nginx/error.log
```

## 🔧 Configuration Files

### Key Files
- `server.js` - Main application server
- `ecosystem.config.js` - PM2 configuration
- `.env` - Environment variables
- `deploy.sh` - Deployment script
- `status.sh` - Status check script

### Environment Configuration
```env
# Server Configuration
PORT=3001
NODE_ENV=production

# Production Configuration
DOMAIN=thepostcloud.info
SERVER_IP=*************

# API Configuration
GEMINI_API_KEY=your-api-key-here
```

## 🔒 Security Features

### Implemented Security
- ✅ CORS configuration for specific domains
- ✅ Security headers (XSS, CSRF protection)
- ✅ Rate limiting on API endpoints
- ✅ Input validation and sanitization
- ✅ Environment variable protection

### SSL Setup (Recommended)
```bash
# Install SSL certificate
sudo certbot --nginx -d thepostcloud.info -d www.thepostcloud.info
```

## 📈 Monitoring

### Health Checks
- **Application**: `curl http://thepostcloud.info/api/health`
- **API Endpoints**: `curl http://thepostcloud.info/api/zodiac-signs`
- **Service Status**: `./status.sh`

### Log Locations
- **Application Logs**: `logs/combined.log`
- **PM2 Logs**: `~/.pm2/logs/`
- **Nginx Logs**: `/var/log/nginx/`

## 🛠️ Troubleshooting

### Common Issues

1. **502 Bad Gateway**
   ```bash
   pm2 restart kubera-manthra
   sudo systemctl restart nginx
   ```

2. **API Not Responding**
   ```bash
   pm2 logs kubera-manthra
   curl http://localhost:3001/api/health
   ```

3. **High Memory Usage**
   ```bash
   pm2 monit
   pm2 restart kubera-manthra
   ```

### Debug Commands
```bash
# Check what's running on port 3001
netstat -tlnp | grep 3001

# Test direct API access
curl http://localhost:3001/api/health

# Check PM2 processes
pm2 list

# View recent logs
tail -50 logs/combined.log
```

## 🔄 Updates and Maintenance

### Application Updates
```bash
# Pull latest changes
git pull origin main

# Install dependencies
npm install --production

# Restart application
pm2 restart kubera-manthra
```

### System Updates
```bash
# Update system packages
sudo yum update -y

# Update Node.js packages
npm update

# Update PM2
sudo npm update -g pm2
```

## 📋 Production Checklist

### Deployment Checklist
- [ ] Domain DNS configured
- [ ] SSL certificate installed
- [ ] Environment variables set
- [ ] Application running via PM2
- [ ] Nginx reverse proxy configured
- [ ] Health checks passing
- [ ] Logs being generated
- [ ] Monitoring set up

### Security Checklist
- [ ] Firewall configured
- [ ] SSH keys only (no password auth)
- [ ] Regular security updates
- [ ] Log monitoring
- [ ] Backup strategy in place

## 📞 Support

### Quick Reference
- **Start**: `pm2 start ecosystem.config.js --env production`
- **Stop**: `pm2 stop kubera-manthra`
- **Restart**: `pm2 restart kubera-manthra`
- **Status**: `./status.sh`
- **Logs**: `pm2 logs kubera-manthra`

### Performance Monitoring
```bash
# Real-time monitoring
pm2 monit

# System resources
htop

# Disk usage
df -h
```

Your Kubera Manthra application is now running in production at **thepostcloud.info**!
