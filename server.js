/**
 * Kubera Manthra Server
 * Modern Express.js server with improved architecture and security
 */

// Load environment variables
require('dotenv').config();

const express = require('express');
const cors = require('cors');
const path = require('path');
const HoroscopeController = require('./controllers/horoscope-controller');

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 3001;

// Initialize controllers
const horoscopeController = new HoroscopeController();

// Middleware setup
app.use(cors({
    origin: process.env.NODE_ENV === 'production' ?
        [
            'https://thepostcloud.info',
            'http://thepostcloud.info',
            'https://www.thepostcloud.info',
            'http://www.thepostcloud.info',
            'http://*************',
            'http://*************:3001'
        ] :
        ['http://localhost:3001', 'http://127.0.0.1:3001'],
    credentials: true
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Security headers
app.use((req, res, next) => {
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    next();
});

// Request logging middleware
app.use((req, res, next) => {
    console.log(`[${new Date().toISOString()}] ${req.method} ${req.path}`);
    next();
});

// Serve static files from the current directory
app.use(express.static('.', {
    maxAge: process.env.NODE_ENV === 'production' ? '1d' : '0'
}));

// API Routes
// Health check endpoint
app.get('/api/health', (req, res) => horoscopeController.healthCheck(req, res));

// Get all zodiac signs
app.get('/api/zodiac-signs', (req, res) => horoscopeController.getZodiacSigns(req, res));

// Get daily horoscope for specific zodiac sign
app.get('/api/daily-horoscope/:zodiacSign', (req, res) => horoscopeController.getDailyHoroscope(req, res));

// Clear cache endpoint (for maintenance)
app.post('/api/clear-cache', (req, res) => horoscopeController.clearCache(req, res));

// Legacy API endpoint for backward compatibility (deprecated)
app.post('/api/openai', (req, res) => {
    console.warn('Legacy /api/openai endpoint called - this endpoint is deprecated');
    res.status(410).json({
        success: false,
        error: 'This endpoint has been deprecated',
        message: 'Please use the new /api/daily-horoscope/:zodiacSign endpoint with Gemini AI',
        migration: {
            oldEndpoint: '/api/openai',
            newEndpoint: '/api/daily-horoscope/{zodiacSign}',
            method: 'GET',
            note: 'Now powered by Google Gemini AI'
        }
    });
});

// Legacy test endpoint (deprecated)
app.get('/api/test-key', (req, res) => {
    console.warn('Legacy /api/test-key endpoint called - this endpoint is deprecated');
    res.status(410).json({
        success: false,
        error: 'This endpoint has been deprecated',
        message: 'Please use the new /api/health endpoint for service status',
        migration: {
            oldEndpoint: '/api/test-key',
            newEndpoint: '/api/health',
            method: 'GET'
        }
    });
});

// Static page routes
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

app.get('/status', (req, res) => {
    res.sendFile(path.join(__dirname, 'status.html'));
});

// Legacy status endpoint (deprecated)
app.get('/api/status', (req, res) => {
    console.warn('Legacy /api/status endpoint called - this endpoint is deprecated');
    res.status(410).json({
        success: false,
        error: 'This endpoint has been deprecated',
        message: 'Please use the new /api/health endpoint for service status',
        migration: {
            oldEndpoint: '/api/status',
            newEndpoint: '/api/health',
            method: 'GET'
        }
    });
});

// 404 handler for API routes
app.use('/api/*', (req, res) => {
    res.status(404).json({
        success: false,
        error: 'API endpoint not found',
        availableEndpoints: [
            'GET /api/health',
            'GET /api/zodiac-signs',
            'GET /api/daily-horoscope/:zodiacSign',
            'POST /api/clear-cache'
        ]
    });
});

// Global error handler
app.use((error, req, res, next) => {
    console.error(`[${new Date().toISOString()}] Unhandled error:`, error);
    res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
    });
});

// Start server
app.listen(PORT, () => {
    console.log(`
╔══════════════════════════════════════════════════════════════╗
║                    Kubera Manthra Server                     ║
╠══════════════════════════════════════════════════════════════╣
║  Server running on: http://localhost:${PORT}                    ║
║  Environment: ${process.env.NODE_ENV || 'development'}                              ║
║  Status dashboard: http://localhost:${PORT}/status             ║
║  API health check: http://localhost:${PORT}/api/health         ║
╚══════════════════════════════════════════════════════════════╝
    `);

    console.log('Available API endpoints:');
    console.log('  GET  /api/health');
    console.log('  GET  /api/zodiac-signs');
    console.log('  GET  /api/daily-horoscope/:zodiacSign');
    console.log('  POST /api/clear-cache');
    console.log('');
});